import { z } from 'zod'

// 充值订单状态
export type RechargeOrderStatus = 'pending' | 'completed' | 'failed' | 'cancelled' | 'expired'

// 支付方式
export type PaymentMethod = 'alipay' | 'wechat' | 'bank_card' | 'balance'

// 充值订单模型
export interface RechargeOrder {
  id: string
  applicationId: string
  amount: string // 充值金额（人民币）
  antCoins: string // 获得的蚁贝数量
  status: RechargeOrderStatus
  paymentMethod: PaymentMethod
  paymentOrderId?: string // 第三方支付订单号
  paymentCallbackData?: Record<string, any> // 支付回调数据
  expiredAt?: Date // 订单过期时间
  completedAt?: Date // 完成时间
  createdAt: Date
  updatedAt: Date
}

// 创建充值订单请求
export interface CreateRechargeOrderRequest {
  applicationId: string
  amount: number // 充值金额
  paymentMethod: PaymentMethod
  returnUrl?: string // 支付成功后跳转地址
  notifyUrl?: string // 支付回调地址
}

// 充值订单查询参数
export interface RechargeOrderListParams {
  page: number
  pageSize: number
  status?: RechargeOrderStatus
  paymentMethod?: PaymentMethod
  startDate?: string
  endDate?: string
}

// 支付回调数据
export interface PaymentCallbackData {
  orderId: string
  paymentOrderId: string
  amount: string
  status: 'success' | 'failed'
  paymentMethod: PaymentMethod
  callbackData: Record<string, any>
  signature?: string // 签名验证
}

// 充值配置
export interface RechargeConfig {
  minAmount: number // 最小充值金额
  maxAmount: number // 最大充值金额
  exchangeRate: number // 人民币到蚁贝的兑换比例 (1元 = ? 蚁贝)
  bonusRules: BonusRule[] // 充值奖励规则
  paymentMethods: PaymentMethodConfig[] // 支持的支付方式
}

// 奖励规则
export interface BonusRule {
  minAmount: number // 最小充值金额
  bonusType: 'percentage' | 'fixed' // 奖励类型：百分比或固定金额
  bonusValue: number // 奖励值
  description: string // 奖励描述
}

// 支付方式配置
export interface PaymentMethodConfig {
  method: PaymentMethod
  isEnabled: boolean
  displayName: string
  icon?: string
  description?: string
  feeRate?: number // 手续费率
}

// Zod Schemas
export const CreateRechargeOrderSchema = z.object({
  amount: z.number().min(1, '充值金额必须大于1元'),
  paymentMethod: z.enum(['alipay', 'wechat', 'bank_card', 'balance']),
  returnUrl: z.string().url().optional(),
  notifyUrl: z.string().url().optional(),
})

export const RechargeOrderListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  status: z.enum(['pending', 'completed', 'failed', 'cancelled', 'expired']).optional(),
  paymentMethod: z.enum(['alipay', 'wechat', 'bank_card', 'balance']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

export const PaymentCallbackSchema = z.object({
  orderId: z.string().uuid(),
  paymentOrderId: z.string(),
  amount: z.string(),
  status: z.enum(['success', 'failed']),
  paymentMethod: z.enum(['alipay', 'wechat', 'bank_card', 'balance']),
  callbackData: z.record(z.any()),
  signature: z.string().optional(),
})

export const RechargeConfigSchema = z.object({
  minAmount: z.number().min(0),
  maxAmount: z.number().min(0),
  exchangeRate: z.number().min(0),
  bonusRules: z.array(z.object({
    minAmount: z.number().min(0),
    bonusType: z.enum(['percentage', 'fixed']),
    bonusValue: z.number().min(0),
    description: z.string(),
  })),
  paymentMethods: z.array(z.object({
    method: z.enum(['alipay', 'wechat', 'bank_card', 'balance']),
    isEnabled: z.boolean(),
    displayName: z.string(),
    icon: z.string().optional(),
    description: z.string().optional(),
    feeRate: z.number().min(0).optional(),
  })),
})
