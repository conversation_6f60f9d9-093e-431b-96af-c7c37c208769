import { z } from 'zod'

// 扣费请求模型
export interface ChargeRequest {
  applicationId: string
  amount: number
  chargeType: 'api_call' | 'traffic' | 'account_quota'
  relatedId: string
  metadata?: Record<string, any>
  description?: string
}

// 扣费结果模型
export interface ChargeResult {
  success: boolean
  transactionId?: string
  beforeBalance: string
  afterBalance: string
  message?: string
}

// 退费请求模型
export interface RefundRequest {
  transactionId: string
  reason?: string
  refundAmount?: number // 如果不指定，则全额退费
}

// 退费结果模型
export interface RefundResult {
  success: boolean
  refundTransactionId?: string
  refundAmount: string
  message?: string
}

// 批量扣费请求
export interface BatchChargeRequest {
  requests: ChargeRequest[]
  failOnFirstError?: boolean // 是否在第一个错误时停止
}

// 批量扣费结果
export interface BatchChargeResult {
  success: boolean
  results: ChargeResult[]
  failedCount: number
  successCount: number
}

// 余额预警配置
export interface BalanceAlert {
  applicationId: string
  alertThreshold: number // 余额低于此值时预警
  isEnabled: boolean
  notificationMethods: ('email' | 'webhook' | 'sms')[]
  webhookUrl?: string
}

// 扣费策略类型
export type BillingStrategyType = 'api_call' | 'traffic' | 'account_quota'

// 定价规则
export interface PricingRule {
  type: 'fixed' | 'tiered' | 'volume_based'
  basePrice: number
  tiers?: PricingTier[]
  metadata?: Record<string, any>
}

// 定价层级
export interface PricingTier {
  minQuantity: number
  maxQuantity?: number
  pricePerUnit: number
}

// 扣费策略
export interface BillingStrategy {
  id: string
  strategyType: BillingStrategyType
  strategyName: string
  pricingRules: PricingRule
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Zod Schemas for validation
export const ChargeRequestSchema = z.object({
  applicationId: z.string().uuid(),
  amount: z.number().min(0.01),
  chargeType: z.enum(['api_call', 'traffic', 'account_quota']),
  relatedId: z.string(),
  metadata: z.record(z.any()).optional(),
  description: z.string().optional(),
})

export const RefundRequestSchema = z.object({
  transactionId: z.string().uuid(),
  reason: z.string().optional(),
  refundAmount: z.number().min(0.01).optional(),
})

export const BalanceAlertSchema = z.object({
  alertThreshold: z.number().min(0),
  isEnabled: z.boolean(),
  notificationMethods: z.array(z.enum(['email', 'webhook', 'sms'])),
  webhookUrl: z.string().url().optional(),
})

export const PricingRuleSchema = z.object({
  type: z.enum(['fixed', 'tiered', 'volume_based']),
  basePrice: z.number().min(0),
  tiers: z.array(z.object({
    minQuantity: z.number().min(0),
    maxQuantity: z.number().min(0).optional(),
    pricePerUnit: z.number().min(0),
  })).optional(),
  metadata: z.record(z.any()).optional(),
})

export const BillingStrategySchema = z.object({
  strategyType: z.enum(['api_call', 'traffic', 'account_quota']),
  strategyName: z.string().min(1),
  pricingRules: PricingRuleSchema,
  isActive: z.boolean(),
})
